"""
Telegram Alert Handlers - Send alerts to Telegram
"""

import logging
from typing import Dict, Any
from datetime import datetime, timezone

from services.telegram.telegram_service import get_telegram_service
from utils.ui_components import format_price_enhanced, format_percentage_enhanced

logger = logging.getLogger(__name__)

class TelegramAlertHandler:
    """Handler for sending alerts to Telegram"""
    
    def __init__(self):
        self.telegram_service = get_telegram_service()
    
    async def handle_price_alert(self, alert_data: Dict[str, Any]):
        """Handle price alerts for Telegram"""
        try:
            symbol = alert_data.get('symbol', '')
            price = alert_data.get('price', 0)
            alert_type = alert_data.get('alert_type', '')
            change_percent = alert_data.get('change_percent', 0)
            timeframe = alert_data.get('timeframe', '')
            
            # Clean symbol name
            clean_symbol = symbol.replace('USDT', '')
            
            # Format price alert message
            if 'target' in alert_type.lower():
                # Price target alert
                message = (
                    f"🎯 *GIÁ: {clean_symbol} ĐẠT TARGET*\n\n"
                    f"💵 Giá: `${price:.4f}`\n"
                    f"🎯 Target đã đạt được"
                )
            else:
                # Percentage change alert
                if change_percent > 0:
                    emoji = "🟢"
                    direction = "TĂNG"
                    sign = "+"
                elif change_percent < 0:
                    emoji = "🔴"
                    direction = "GIẢM"
                    sign = ""
                else:
                    emoji = "⚪"
                    direction = "KHÔNG ĐỔI"
                    sign = ""
                
                if timeframe:
                    timeframe_text = f" ({timeframe})"
                else:
                    timeframe_text = ""
                
                message = (
                    f"{emoji} *GIÁ: {clean_symbol} {direction}{timeframe_text}*\n\n"
                    f"💵 Giá: `${price:.4f}`\n"
                    f"📊 Thay đổi: `{sign}{change_percent:.2f}%`"
                )
            
            await self.telegram_service.send_message(message)
            logger.info(f"Sent price alert to Telegram: {symbol} - {alert_type}")
            
        except Exception as e:
            logger.error(f"Error sending price alert to Telegram: {e}")
    
    async def handle_volume_alert(self, alert_data: Dict[str, Any]):
        """Handle volume alerts for Telegram"""
        try:
            symbol = alert_data.get('symbol', '')
            volume_ratio = alert_data.get('volume_ratio', 0)
            price = alert_data.get('price', 0)
            change_percent = alert_data.get('change_percent', 0)
            
            # Clean symbol name
            clean_symbol = symbol.replace('USDT', '')
            
            # Determine direction emoji
            if change_percent > 5:
                emoji = "🟢"
                direction = "TĂNG MẠNH"
            elif change_percent < -5:
                emoji = "🔴"
                direction = "GIẢM MẠNH"
            elif change_percent > 0:
                emoji = "🟢"
                direction = "TĂNG"
            elif change_percent < 0:
                emoji = "🔴"
                direction = "GIẢM"
            else:
                emoji = "⚪"
                direction = "KHÔNG ĐỔI"
            
            sign = "+" if change_percent > 0 else ""
            
            message = (
                f"{emoji} *VOL: {clean_symbol} {direction}*\n\n"
                f"💵 Giá: `${price:.4f}`\n"
                f"📊 24h: `{sign}{change_percent:.2f}%`\n"
                f"📈 Volume: `{volume_ratio:.1f}x MA`"
            )
            
            await self.telegram_service.send_message(message)
            logger.info(f"Sent volume alert to Telegram: {symbol} - {volume_ratio:.1f}x")
            
        except Exception as e:
            logger.error(f"Error sending volume alert to Telegram: {e}")
    


# Global instance
_telegram_alert_handler = None

def get_telegram_alert_handler() -> TelegramAlertHandler:
    """Get the global Telegram alert handler instance"""
    global _telegram_alert_handler
    if _telegram_alert_handler is None:
        _telegram_alert_handler = TelegramAlertHandler()
    return _telegram_alert_handler
