"""
Telegram Command Handlers - Handle commands from Telegram
"""

import logging
import time
from typing import Dict, Any
from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import TelegramError

from services.market.market_service import get_market_service
from services.market.market_monitor_service import get_market_monitor_service
from services.market.mt5_data_service import get_mt5_service
from utils.ui_components import format_price_enhanced, format_percentage_enhanced

logger = logging.getLogger(__name__)

class TelegramCommandHandler:
    """Handler for Telegram commands"""

    def __init__(self):
        self.market_service = get_market_service()
        self.monitor_service = get_market_monitor_service()
        self.mt5_service = get_mt5_service()
        self.previous_prices = {}

        # Store pinned watchlist message ID for auto-refresh
        self.pinned_watchlist_message_id = None
        self.last_watchlist_update = None
    
    async def handle_watchlist_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /watchlist command"""
        try:
            # Send typing indicator
            await update.message.reply_chat_action('typing')
            
            # Get watchlist data
            watchlist_data = await self.market_service.get_watchlist_data()
            
            if not watchlist_data['success']:
                await update.message.reply_text(
                    f"❌ Lỗi khi lấy dữ liệu: {watchlist_data['error']}"
                )
                return
            
            # Calculate price changes
            watchlist_symbols = watchlist_data.get('data', {})
            current_prices = {}

            for symbol, data in watchlist_symbols.items():
                if isinstance(data, dict) and 'price' in data:
                    current_prices[symbol] = data['price']
                else:
                    logger.warning(f"Invalid data structure for symbol {symbol}: {data}")
                    current_prices[symbol] = 0.0
            
            price_changes = {}
            for symbol, current_price in current_prices.items():
                if symbol in self.previous_prices:
                    previous_price = self.previous_prices[symbol]
                    change = ((current_price - previous_price) / previous_price) * 100
                    price_changes[symbol] = change
                else:
                    price_changes[symbol] = 0.0
            
            self.previous_prices.update(current_prices)
            
            # Format watchlist message
            message = await self.format_watchlist_message(watchlist_data, price_changes)

            # Send message and pin it
            sent_message = await update.message.reply_text(message, parse_mode='Markdown')

            # Pin the message and store message ID for auto-refresh
            if sent_message:
                try:
                    await update.message.get_bot().pin_chat_message(
                        chat_id=update.effective_chat.id,
                        message_id=sent_message.message_id,
                        disable_notification=True
                    )
                    self.pinned_watchlist_message_id = sent_message.message_id
                    self.last_watchlist_update = time.time()
                    logger.info(f"Pinned watchlist message {sent_message.message_id}")
                except Exception as e:
                    logger.warning(f"Failed to pin watchlist message: {e}")

            logger.info(f"Watchlist command executed for Telegram user {update.effective_user.id}")
            
        except Exception as e:
            logger.error(f"Error in Telegram watchlist command: {e}")
            await update.message.reply_text(
                "❌ Có lỗi xảy ra khi lấy dữ liệu watchlist. Vui lòng thử lại sau."
            )

    async def auto_refresh_watchlist(self):
        """Auto-refresh pinned watchlist message every 90 seconds"""
        if not self.pinned_watchlist_message_id:
            return

        try:
            # Get fresh watchlist data
            watchlist_data = await self.market_service.get_watchlist_data()

            if not watchlist_data['success']:
                logger.warning(f"Failed to get watchlist data for auto-refresh: {watchlist_data['error']}")
                return

            # Calculate price changes
            current_prices = {}
            watchlist_symbols = watchlist_data.get('data', {})

            for symbol, data in watchlist_symbols.items():
                if isinstance(data, dict) and 'price' in data:
                    current_prices[symbol] = data['price']
                else:
                    current_prices[symbol] = 0.0

            price_changes = {}
            for symbol, current_price in current_prices.items():
                if symbol in self.previous_prices:
                    previous_price = self.previous_prices[symbol]
                    if previous_price > 0:
                        change = ((current_price - previous_price) / previous_price) * 100
                        price_changes[symbol] = change
                    else:
                        price_changes[symbol] = 0.0
                else:
                    price_changes[symbol] = 0.0

            self.previous_prices.update(current_prices)

            # Format updated message
            message = await self.format_watchlist_message(watchlist_data, price_changes)

            # Edit the pinned message
            from services.telegram.telegram_service import get_telegram_service
            telegram_service = get_telegram_service()

            success = await telegram_service.edit_message(
                self.pinned_watchlist_message_id,
                message
            )

            if success:
                self.last_watchlist_update = time.time()
                logger.debug("Auto-refreshed Telegram watchlist")
            else:
                logger.warning("Failed to auto-refresh Telegram watchlist")

        except Exception as e:
            logger.error(f"Error in auto-refresh watchlist: {e}")
    
    async def format_watchlist_message(self, watchlist_data: Dict[str, Any], price_changes: Dict[str, float]) -> str:
        """Format watchlist data for Telegram message with better alignment"""
        try:
            # Header
            message_parts = ["🚀 *CRYPTO WATCHLIST* 📈"]
            message_parts.append("")

            # Add market indicators section at the top
            try:
                # Fetch market overview and rates data
                market_overview = await self.market_service.fetch_market_overview()
                rates_data = await self.monitor_service.get_current_rates()

                btcdom_data = market_overview.get('btcdom', {})
                paxg_data = market_overview.get('paxg', {})
                p2p_data = rates_data.get('p2p', {})
                earn_data = rates_data.get('earn', {})

                btcdom_index = btcdom_data.get('index', 0)
                btcdom_change = btcdom_data.get('change_24h', 0)
                paxg_price = paxg_data.get('price', 0)
                paxg_change = paxg_data.get('change_24h', 0)

                # Get XAU data from MT5 service
                xau_price = 0
                xau_change = 0
                try:
                    xau_data = await self.mt5_service.get_real_time_price('XAUUSD')
                    if xau_data and xau_data.get('success'):
                        xau_price = xau_data.get('current_price', 0)
                        xau_change = xau_data.get('change_percent', 0)
                except Exception as e:
                    logger.warning(f"Failed to get XAU data: {e}")

                # P2P USDT selling rate and APY data
                usdt_sell_rate = p2p_data.get('sell_rate', 0)
                flexible_data = earn_data.get('flexible', {})
                apy_rate = flexible_data.get('rate', 0)

                # Format market indicators with monospace
                message_parts.append("*📊 MARKET INDICATORS*")
                message_parts.append("```")

                # Format BTCDOM with Bitcoin icon
                btcdom_emoji = "🟢" if btcdom_change >= 0 else "🔴"
                message_parts.append(f"₿  BTCDOM: {btcdom_index:>5.0f}       {btcdom_emoji} {btcdom_change:>4.1f}%")

                # Format PAXG with gold bar icon
                paxg_emoji = "🟢" if paxg_change >= 0 else "🔴"
                message_parts.append(f"🧈 PAXG:   ${paxg_price:>4.0f}       {paxg_emoji} {paxg_change:>4.1f}%")

                # Format XAU (Gold) with gold icon
                if xau_price > 0:
                    xau_emoji = "🟢" if xau_change >= 0 else "🔴"
                    message_parts.append(f"🥇 XAU:    ${xau_price:>4.0f}       {xau_emoji} {xau_change:>4.1f}%")
                else:
                    message_parts.append(f"🥇 XAU:    ${xau_price:>4.0f}       ⚪  0.0%")

                # Format P2P USDT selling rate
                if usdt_sell_rate > 0:
                    message_parts.append(f"💸 USDT:   {usdt_sell_rate:>6,.0f}")
                else:
                    message_parts.append(f"💸 USDT:   N/A")

                # Format APY rate with consistent spacing
                if apy_rate > 0:
                    # Handle both percentage and decimal formats
                    if apy_rate > 1:
                        apy_display = f"{apy_rate:.2f}%"
                    else:
                        apy_display = f"{apy_rate*100:.2f}%"
                    message_parts.append(f"💎 APY:    {apy_display:>8}")
                else:
                    message_parts.append(f"💎 APY:    {'N/A':>8}")

                message_parts.append("```")
                message_parts.append("")

            except Exception as e:
                logger.error(f"Error fetching market indicators: {e}")
                message_parts.append("*📊 MARKET INDICATORS*")
                message_parts.append("```")
                message_parts.append("₿  BTCDOM: N/A         N/A")
                message_parts.append("🧈 PAXG:   N/A         N/A")
                message_parts.append("🥇 XAU:    N/A         N/A")
                message_parts.append("💸 USDT:   N/A")
                message_parts.append(f"💎 APY:    {'N/A':>8}")
                message_parts.append("```")
                message_parts.append("")

            # Get comprehensive market data for watchlist symbols
            prices = watchlist_data.get('prices', {})
            if not prices:
                # Try to get from 'data' key (legacy format)
                watchlist_symbols = watchlist_data.get('data', {})
                prices = {symbol: data.get('price', 0) for symbol, data in watchlist_symbols.items() if isinstance(data, dict)}

            if prices:
                market_data = await self._fetch_comprehensive_market_data(list(prices.keys()), "daily_open")
            else:
                market_data = {}

            # Crypto watchlist section
            message_parts.append("```")
            message_parts.append("TOKEN  PRICE     CHG        VOL")
            message_parts.append("──────────────────────────────")

            up_count = 0
            down_count = 0

            # Process watchlist data and sort by volume
            token_list = []
            if prices:
                for symbol, price in prices.items():
                    symbol_data = market_data.get(symbol, {})
                    daily_change = symbol_data.get('daily_change', 0.0)
                    volume_24h = symbol_data.get('volume_24h', 0.0)

                    # Count up/down for footer
                    if daily_change > 0:
                        up_count += 1
                    elif daily_change < 0:
                        down_count += 1

                    # Clean symbol name
                    clean_symbol = symbol.replace('USDT', '')

                    # Format price based on value
                    if price >= 1000:
                        price_str = f"${price:,.0f}"
                    elif price >= 1:
                        price_str = f"${price:.2f}"
                    else:
                        price_str = f"${price:.4f}"

                    # Format change with emoji
                    if daily_change > 0:
                        change_emoji = "🟢"
                        change_str = f"+{daily_change:.2f}%"
                    elif daily_change < 0:
                        change_emoji = "🔴"
                        change_str = f"{daily_change:.2f}%"
                    else:
                        change_emoji = "⚪"
                        change_str = f"{daily_change:.2f}%"

                    # Format volume
                    if volume_24h >= 1e9:
                        volume_str = f"{volume_24h/1e9:.1f}B"
                    elif volume_24h >= 1e6:
                        volume_str = f"{volume_24h/1e6:.1f}M"
                    elif volume_24h >= 1e3:
                        volume_str = f"{volume_24h/1e3:.1f}K"
                    else:
                        volume_str = f"{volume_24h:.0f}"

                    # Store token data for sorting
                    token_list.append({
                        'symbol': clean_symbol,
                        'price_str': price_str,
                        'change_emoji': change_emoji,
                        'change_str': change_str,
                        'volume_str': volume_str,
                        'volume_24h': volume_24h
                    })

                # Sort by volume (highest first)
                token_list.sort(key=lambda x: x['volume_24h'], reverse=True)

                # Add sorted tokens to message
                for token in token_list:
                    # Improved column alignment with better spacing
                    token_pad = ' ' * max(0, 7 - len(token['symbol']))
                    price_pad = ' ' * max(0, 10 - len(token['price_str']))
                    # Calculate change column width including emoji
                    change_with_emoji = f"{token['change_emoji']}{token['change_str']}"
                    change_pad = ' ' * max(0, 15 - len(token['change_str']) - 1)  # Increased padding for VOL alignment

                    message_parts.append(f"{token['symbol']}{token_pad}{token['price_str']}{price_pad}{change_with_emoji}{change_pad}{token['volume_str']}")

            message_parts.append("```")

            # Footer with counts
            message_parts.append("")
            message_parts.append(f"📈 {up_count} tăng | 📉 {down_count} giảm")
            message_parts.append(f"⏰ _Cập nhật: {time.strftime('%H:%M:%S')}_")

            return "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error formatting watchlist message: {e}")
            return "❌ Lỗi khi định dạng dữ liệu watchlist"

    async def _fetch_comprehensive_market_data(self, symbols: list, change_type: str = "24h") -> dict:
        """Fetch comprehensive market data including daily change and volume"""
        try:
            import asyncio
            from services.market.market_service import get_binance_futures_exchange

            exchange = get_binance_futures_exchange()
            market_data = {}

            formatted_symbols = []
            for symbol in symbols:
                formatted = f"{symbol.replace('USDT', '')}/USDT:USDT"
                formatted_symbols.append(formatted)

            loop = asyncio.get_event_loop()
            tickers = await loop.run_in_executor(None, exchange.fetch_tickers, formatted_symbols)

            for symbol in symbols:
                formatted_symbol = f"{symbol.replace('USDT', '')}/USDT:USDT"

                if formatted_symbol in tickers:
                    ticker = tickers[formatted_symbol]

                    # Use unified percentage calculation service
                    from services.market.percentage_calculation_service import get_percentage_service
                    percentage_service = get_percentage_service()

                    # Choose calculation method based on change_type
                    if change_type == "daily_open":
                        # Get daily candle data for true daily open calculation
                        daily_candle_data = await self._get_daily_candle_data(symbol, exchange)
                        percentage_result = percentage_service.extract_binance_daily_open_percentage(ticker, daily_candle_data)
                    else:  # default to 24h rolling change
                        percentage_result = percentage_service.extract_binance_percentage(ticker)

                    daily_change = percentage_result.value if percentage_result.is_valid else 0.0
                    volume_24h = ticker.get('quoteVolume', 0.0) or 0.0

                    market_data[symbol] = {
                        'daily_change': daily_change,
                        'volume_24h': volume_24h,
                        'percentage_source': percentage_result.source.value,
                        'percentage_method': percentage_result.method.value
                    }
                else:
                    market_data[symbol] = {
                        'daily_change': 0.0,
                        'volume_24h': 0.0
                    }

            logger.info(f"Fetched comprehensive market data for {len(market_data)} symbols")
            return market_data

        except Exception as e:
            logger.error(f"Error fetching comprehensive market data: {e}")
            return {symbol: {'daily_change': 0.0, 'volume_24h': 0.0} for symbol in symbols}

    async def _get_daily_candle_data(self, symbol: str, exchange) -> dict:
        """Get daily candle data for daily open calculation"""
        try:
            import asyncio

            # Format symbol for exchange
            formatted_symbol = f"{symbol.replace('USDT', '')}/USDT:USDT"

            # Get last 2 daily candles (yesterday and today)
            loop = asyncio.get_event_loop()
            ohlcv = await loop.run_in_executor(None, exchange.fetch_ohlcv, formatted_symbol, '1d', None, 2)

            if len(ohlcv) >= 1:
                # ohlcv format: [timestamp, open, high, low, close, volume]
                today_candle = ohlcv[-1]  # Current day candle
                today_open = today_candle[1]  # Open price of today's candle

                return {
                    'daily_open': today_open,
                    'timestamp': today_candle[0]
                }
            else:
                logger.warning(f"No daily candle data available for {symbol}")
                return {}

        except Exception as e:
            logger.error(f"Error getting daily candle data for {symbol}: {e}")
            return {}
    


# Global instance
_telegram_command_handler = None

def get_telegram_command_handler() -> TelegramCommandHandler:
    """Get the global Telegram command handler instance"""
    global _telegram_command_handler
    if _telegram_command_handler is None:
        _telegram_command_handler = TelegramCommandHandler()
    return _telegram_command_handler
