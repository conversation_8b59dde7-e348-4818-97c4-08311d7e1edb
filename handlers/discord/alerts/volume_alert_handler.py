#!/usr/bin/env python3
"""
Discord Volume Alert Handler - Sends volume spike alerts to Discord and Telegram
"""

import logging
import discord
from datetime import datetime, timezone
from typing import Dict, List, Any

from utils.config import get_guild_id
from utils.constants import EMOJI, DISCORD_FORMATTING

logger = logging.getLogger(__name__)

class VolumeAlertHandler:
    """Handler for sending volume alerts to Discord and Telegram"""

    def __init__(self, bot):
        self.bot = bot
        self.guild_id = get_guild_id()
        self.channel_name = "🚨-alerts"  # Target channel name

        # Initialize Telegram alert handler
        from handlers.telegram.telegram_alerts import get_telegram_alert_handler
        self.telegram_handler = get_telegram_alert_handler()
    
    async def handle_volume_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Process volume alerts and send to Discord and Telegram"""
        try:
            if alert_type == "volume_spike":
                # Send to Discord
                embed = await self._create_volume_spike_embed(alert_data)
                await self._send_to_alerts_channel(embed)

                # Send to Telegram
                alerts = alert_data.get('alerts', [])
                for alert in alerts:
                    # Get current price and daily change for the symbol
                    symbol = alert.get('symbol', '')
                    price = alert.get('price', 0)

                    # Get daily change from comprehensive price data
                    try:
                        from services.market.market_service import get_market_service
                        market_service = get_market_service()
                        comprehensive_data = await market_service.get_comprehensive_price_data(symbol)
                        change_percent = comprehensive_data.get('daily_change_percent', 0) if comprehensive_data else 0
                    except Exception as e:
                        logger.warning(f"Failed to get daily change for {symbol}: {e}")
                        change_percent = 0

                    telegram_alert_data = {
                        'symbol': symbol,
                        'volume_ratio': alert.get('volume_ratio', 0),
                        'price': price,
                        'change_percent': change_percent
                    }
                    await self.telegram_handler.handle_volume_alert(telegram_alert_data)
            else:
                logger.warning(f"Unknown volume alert type: {alert_type}")

        except Exception as e:
            logger.error(f"Error handling volume alert: {e}")
    
    async def _create_volume_spike_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        """Create embed for volume spike alerts"""
        alerts = alert_data.get('alerts', [])

        if not alerts:
            return None

        # Create simple embed
        embed = discord.Embed(
            color=0x00ff88,  # Green color
            timestamp=datetime.now(timezone.utc)
        )

        # Process each alert individually
        alert_text = ""
        for alert in alerts:
            symbol = alert['symbol'].replace('USDT', '')
            timeframe = alert['timeframe'].upper()
            ratio = alert['volume_ratio']
            price = alert.get('price', 0)
            threshold = alert['threshold']

            # Determine emoji and text based on threshold
            if threshold >= 2.2:
                emoji = "🔥"
                intensity = "TĂNG MẠNH"
            else:
                emoji = "🟢"
                intensity = "TĂNG MẠNH"

            # Format like the example with VOL: prefix
            alert_text += f"VOL: {emoji} **{symbol}** {intensity}\n"
            if price > 0:
                alert_text += f"💵 Giá: ${price:,.4f}\n"
            alert_text += f"📊 Volume: {ratio:.1f}x MA20 ({timeframe})\n\n"

        embed.description = alert_text.strip()

        # Simple footer
        embed.set_footer(text="Volume Alert • Watchlist")

        return embed
    
    async def _send_to_alerts_channel(self, embed: discord.Embed):
        """Send alert to the 🚨-alerts channel"""
        if not embed:
            return
            
        if self.guild_id:
            try:
                guild = self.bot.get_guild(int(self.guild_id))
                if guild:
                    # Find alerts channel
                    target_channel = None
                    for channel in guild.channels:
                        if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                            if self.channel_name.lower() in channel.name.lower():
                                target_channel = channel
                                break
                    
                    # Create channel if it doesn't exist
                    if not target_channel:
                        try:
                            target_channel = await guild.create_text_channel(
                                name=self.channel_name,
                                topic="Volume spike alerts for watchlist symbols"
                            )
                            logger.info(f"Created alerts channel: #{target_channel.name}")
                        except discord.Forbidden:
                            logger.error("No permission to create alerts channel")
                            return
                        except Exception as e:
                            logger.error(f"Failed to create alerts channel: {e}")
                            return
                    
                    # Send alert
                    if target_channel:
                        await target_channel.send(embed=embed)
                        logger.info(f"Sent volume alert to #{target_channel.name}")
                    else:
                        logger.warning("No suitable channel found for volume alert")
                else:
                    logger.warning(f"Guild not found: {self.guild_id}")
            except Exception as e:
                logger.error(f"Failed to send volume alert to guild: {e}")
        else:
            logger.warning("No guild_id configured for volume alerts")

# Global handler instance
_volume_alert_handler = None

def get_volume_alert_handler(bot) -> VolumeAlertHandler:
    """Get or create volume alert handler instance"""
    global _volume_alert_handler
    if _volume_alert_handler is None:
        _volume_alert_handler = VolumeAlertHandler(bot)
    return _volume_alert_handler
