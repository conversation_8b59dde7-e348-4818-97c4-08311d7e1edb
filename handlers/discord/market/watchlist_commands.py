import logging
import time
import discord
from discord.ext import commands
from discord import app_commands

from services.market.market_service import get_market_service

logger = logging.getLogger(__name__)

class WatchlistCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.market_service = get_market_service()

        # Import market monitor service for P2P/APY data
        from services.market.market_monitor_service import get_market_monitor_service
        self.monitor_service = get_market_monitor_service()

        # Import MT5 service for XAU data
        from services.market.mt5_data_service import get_mt5_service
        self.mt5_service = get_mt5_service()

        self.previous_prices = {}

    @app_commands.command(name="watchlist", description="Display cryptocurrency watchlist with current prices and auto-refresh")
    @app_commands.describe(
        auto_refresh="Enable auto-refresh for this watchlist (default: True)",
        change_type="Type of daily change calculation (default: 24h)"
    )
    @app_commands.choices(change_type=[
        app_commands.Choice(name="24H Rolling Change", value="24h"),
        app_commands.Choice(name="Daily Open Change", value="daily_open")
    ])
    async def watchlist(self, interaction: discord.Interaction, auto_refresh: bool = True, change_type: str = "24h"):
        start_time = time.time()

        if not self.bot.check_cooldown(interaction.user.id, "watchlist", 10):
            remaining = self.bot.get_cooldown_remaining(interaction.user.id, "watchlist", 10)
            await interaction.response.send_message(
                f"⏰ Please wait {remaining:.1f} seconds before using watchlist again.",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            # Get shared watchlist data from cache service
            from services.data.shared_watchlist_cache import get_shared_watchlist_cache
            cache_service = get_shared_watchlist_cache()

            watchlist_data = await cache_service.get_watchlist_data()

            if not watchlist_data['success']:
                await interaction.followup.send(watchlist_data['error'])
                await self.bot.record_command_execution("watchlist", start_time, False)
                return

            current_prices = watchlist_data['prices']
            price_changes = {}

            for symbol, price in current_prices.items():
                if symbol in self.previous_prices:
                    old_price = self.previous_prices[symbol]

                    # Use unified percentage calculation service
                    from services.market.percentage_calculation_service import get_percentage_service
                    percentage_service = get_percentage_service()

                    result = percentage_service.calculate_percentage_change(price, old_price)
                    change_pct = result.value if result.is_valid else 0.0
                    price_changes[symbol] = change_pct
                else:
                    price_changes[symbol] = 0.0

            self.previous_prices.update(current_prices)

            embed = await self.create_enhanced_watchlist_embed(watchlist_data, price_changes, change_type)

            view = WatchlistView(self, auto_refresh, change_type) if auto_refresh else None

            message = await interaction.followup.send(embed=embed, view=view)

            if auto_refresh and message:
                # Store both message_id and change_type for auto-refresh
                self.bot.active_watchlists[interaction.channel.id] = {
                    'message_id': message.id,
                    'change_type': change_type
                }
                logger.info(f"Added watchlist message to auto-refresh tracking: {message.id} with change_type: {change_type}")

            await self.bot.record_command_execution("watchlist", start_time, True)
            logger.info(f"Enhanced watchlist command executed by {interaction.user} ({interaction.user.id})")

        except Exception as e:
            logger.error(f"Error in watchlist command: {e}")
            await interaction.followup.send("❌ An error occurred while fetching watchlist data.")
            await self.bot.record_command_execution("watchlist", start_time, False)

    async def create_watchlist_embed(self, watchlist_data, change_type: str = "24h") -> discord.Embed:
        return await self.create_enhanced_watchlist_embed(watchlist_data, {}, change_type)

    async def create_enhanced_watchlist_embed(self, watchlist_data, price_changes: dict, change_type: str = "24h") -> discord.Embed:
        embed = discord.Embed(
            title="🚀 CRYPTO WATCHLIST 📈",
            color=0x00ff88,
            timestamp=discord.utils.utcnow()
        )

        prices = watchlist_data['prices']
        if prices:
            market_data = await self._fetch_comprehensive_market_data(list(prices.keys()), change_type)

            description = ""
            token_data = []
            for symbol, price in prices.items():
                token = symbol.replace('USDT', '')
                symbol_data = market_data.get(symbol, {})
                daily_change = symbol_data.get('daily_change', 0.0)
                volume_24h = symbol_data.get('volume_24h', 0.0)
                token_data.append({
                    'token': token,
                    'price': price,
                    'daily_change': daily_change,
                    'volume_24h': volume_24h
                })

            token_data.sort(key=lambda x: x['volume_24h'], reverse=True)

            # Add market indicators section at the top
            try:
                # Fetch market overview and rates data
                market_overview = await self.market_service.fetch_market_overview()
                rates_data = await self.monitor_service.get_current_rates()

                btcdom_data = market_overview.get('btcdom', {})
                paxg_data = market_overview.get('paxg', {})
                p2p_data = rates_data.get('p2p', {})
                earn_data = rates_data.get('earn', {})

                btcdom_index = btcdom_data.get('index', 0)
                btcdom_change = btcdom_data.get('change_24h', 0)
                paxg_price = paxg_data.get('price', 0)
                paxg_change = paxg_data.get('change_24h', 0)

                # Fetch XAU (Gold) data from MT5 service
                xau_data = await self.mt5_service.get_real_time_price('XAUUSD')
                xau_price = 0
                xau_change = 0
                if xau_data.get('success'):
                    xau_price = xau_data.get('current_price', 0)
                    xau_change = xau_data.get('change_percent', 0)

                # P2P USDT selling rate and APY data
                usdt_sell_rate = p2p_data.get('sell_rate', 0)
                flexible_data = earn_data.get('flexible', {})
                apy_rate = flexible_data.get('rate', 0)

                # Format market indicators with proper column alignment using monospace
                description += "**📊 MARKET INDICATORS**\n```\n"

                # Format BTCDOM with Bitcoin icon
                btcdom_emoji = "🟢" if btcdom_change >= 0 else "🔴"
                description += f"₿  BTCDOM: {btcdom_index:>5.0f}       {btcdom_emoji} {btcdom_change:>4.1f}%\n"

                # Format PAXG with gold bar icon
                paxg_emoji = "🟢" if paxg_change >= 0 else "🔴"
                description += f"🧈 PAXG:   ${paxg_price:>4.0f}       {paxg_emoji} {paxg_change:>4.1f}%\n"

                # Format XAU (Gold) with gold icon
                if xau_price > 0:
                    xau_emoji = "🟢" if xau_change >= 0 else "🔴"
                    description += f"🥇 XAU:    ${xau_price:>4.0f}       {xau_emoji} {xau_change:>4.1f}%\n"
                else:
                    description += f"🥇 XAU:    N/A         N/A\n"

                # Format P2P USDT selling rate
                if usdt_sell_rate > 0:
                    description += f"💸 USDT:   {usdt_sell_rate:>6,.0f}\n"
                else:
                    description += f"💸 USDT:   N/A\n"

                # Format APY rate with consistent spacing
                if apy_rate > 0:
                    # Handle both percentage and decimal formats
                    if apy_rate > 1:
                        apy_display = f"{apy_rate:.2f}%"
                    else:
                        apy_display = f"{apy_rate*100:.2f}%"
                    description += f"💎 APY:    {apy_display:>8}\n"
                else:
                    description += f"💎 APY:    {'N/A':>8}\n"

                description += "```\n"

            except Exception as e:
                logger.error(f"Error fetching market indicators: {e}")
                description += "**📊 MARKET INDICATORS**\n```\n"
                description += "₿  BTCDOM: N/A         N/A\n"
                description += "🧈 PAXG:   N/A         N/A\n"
                description += "🥇 XAU:    N/A         N/A\n"
                description += "💸 USDT:   N/A\n"
                description += f"💎 APY:    {'N/A':>8}\n"
                description += "```\n"

            description += "```\n"
            change_header = "24H CHG" if change_type == "24h" else "CHG"
            description += f"TOKEN  PRICE     {change_header}       VOL\n"
            description += "────────────────────────────────────────\n"

            up_count = 0
            down_count = 0

            for data in token_data:
                token = data['token']
                price = data['price']
                daily_change = data['daily_change']
                volume_24h = data['volume_24h']

                if price < 0.1:
                    price_str = f"${price:.4f}"
                elif price < 1:
                    price_str = f"${price:.4f}"
                elif price < 100:
                    price_str = f"${price:.2f}"
                else:
                    price_str = f"${price:,.0f}"

                if daily_change > 0:
                    change_str = f"🟢+{daily_change:.2f}%"
                    up_count += 1
                elif daily_change < 0:
                    change_str = f"🔴{daily_change:.2f}%"
                    down_count += 1
                else:
                    change_str = f"⚪{daily_change:.2f}%"

                if volume_24h >= 1_000_000_000:
                    volume_str = f"{volume_24h/1_000_000_000:,.1f}B"
                elif volume_24h >= 1_000_000:
                    if volume_24h >= 100_000_000:
                        volume_str = f"{volume_24h/1_000_000:,.1f}M"
                    else:
                        volume_str = f"{volume_24h/1_000_000:,.1f}M"
                elif volume_24h >= 1_000:
                    volume_str = f"{volume_24h/1_000:,.1f}K"
                else:
                    volume_str = f"{volume_24h:,.1f}"

                # Improved column alignment with better spacing
                token_pad = ' ' * max(0, 7 - len(token))
                price_pad = ' ' * max(0, 10 - len(price_str))
                change_pad = ' ' * max(0, 12 - len(change_str))

                description += f"{token}{token_pad}{price_str}{price_pad}{change_str}{change_pad}{volume_str}\n"

            description += "```\n"
            description += f"**📈 {up_count} tăng | 📉 {down_count} giảm**"

            embed.description = description
        else:
            embed.description = "❌ Không có dữ liệu giá"

        embed.set_footer(text="Dữ liệu từ Binance Futures • Tự động cập nhật mỗi 90s")

        embed.set_thumbnail(url="https://cryptologos.cc/logos/bitcoin-btc-logo.png")

        return embed

    async def _fetch_comprehensive_market_data(self, symbols: list, change_type: str = "24h") -> dict:
        try:
            import asyncio

            from services.market.market_service import get_binance_futures_exchange
            exchange = get_binance_futures_exchange()

            formatted_symbols = []
            for symbol in symbols:
                formatted = f"{symbol.replace('USDT', '')}/USDT:USDT"
                formatted_symbols.append(formatted)

            loop = asyncio.get_event_loop()
            tickers = await loop.run_in_executor(None, exchange.fetch_tickers, formatted_symbols)

            market_data = {}
            for symbol in symbols:
                formatted_symbol = f"{symbol.replace('USDT', '')}/USDT:USDT"

                if formatted_symbol in tickers:
                    ticker = tickers[formatted_symbol]

                    # Use unified percentage calculation service
                    from services.market.percentage_calculation_service import get_percentage_service
                    percentage_service = get_percentage_service()

                    # Choose calculation method based on change_type
                    if change_type == "daily_open":
                        # Get daily candle data for true daily open calculation
                        daily_candle_data = await self._get_daily_candle_data(symbol, exchange)
                        percentage_result = percentage_service.extract_binance_daily_open_percentage(ticker, daily_candle_data)
                    else:  # default to 24h rolling change
                        percentage_result = percentage_service.extract_binance_percentage(ticker)

                    daily_change = percentage_result.value if percentage_result.is_valid else 0.0
                    volume_24h = ticker.get('quoteVolume', 0.0) or 0.0

                    market_data[symbol] = {
                        'daily_change': daily_change,
                        'volume_24h': volume_24h,
                        'percentage_source': percentage_result.source.value,
                        'percentage_method': percentage_result.method.value
                    }
                else:
                    market_data[symbol] = {
                        'daily_change': 0.0,
                        'volume_24h': 0.0
                    }

            logger.info(f"Fetched comprehensive market data for {len(market_data)} symbols")
            return market_data

        except Exception as e:
            logger.error(f"Error fetching comprehensive market data: {e}")
            return {symbol: {'daily_change': 0.0, 'volume_24h': 0.0} for symbol in symbols}

    async def _get_daily_candle_data(self, symbol: str, exchange) -> dict:
        """
        Get daily candle data to extract today's opening price

        Args:
            symbol: Trading symbol (e.g., BTCUSDT)
            exchange: Binance exchange instance

        Returns:
            Dictionary with daily_open price or empty dict if failed
        """
        try:
            import asyncio

            # Format symbol for exchange
            formatted_symbol = f"{symbol.replace('USDT', '')}/USDT:USDT"

            # Get last 2 daily candles (yesterday and today)
            loop = asyncio.get_event_loop()
            ohlcv = await loop.run_in_executor(None, exchange.fetch_ohlcv, formatted_symbol, '1d', None, 2)

            if len(ohlcv) >= 1:
                # ohlcv format: [timestamp, open, high, low, close, volume]
                today_candle = ohlcv[-1]  # Current day candle
                today_open = today_candle[1]  # Open price of today's candle

                return {
                    'daily_open': today_open,
                    'timestamp': today_candle[0]
                }
            else:
                logger.warning(f"No daily candle data available for {symbol}")
                return {}

        except Exception as e:
            logger.error(f"Error getting daily candle data for {symbol}: {e}")
            return {}

class WatchlistView(discord.ui.View):
    def __init__(self, watchlist_cog, auto_refresh: bool = True, change_type: str = "24h"):
        super().__init__(timeout=300)
        self.watchlist_cog = watchlist_cog
        self.auto_refresh = auto_refresh
        self.change_type = change_type

    @discord.ui.button(label="🔄 Refresh", style=discord.ButtonStyle.primary)
    async def refresh_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if not self.watchlist_cog.bot.check_cooldown(interaction.user.id, "watchlist_refresh", 5):
            remaining = self.watchlist_cog.bot.get_cooldown_remaining(interaction.user.id, "watchlist_refresh", 5)
            await interaction.response.send_message(
                f"⏰ Please wait {remaining:.1f} seconds before refreshing again.",
                ephemeral=True
            )
            return

        await interaction.response.defer()

        try:
            # Get shared watchlist data from cache service
            from services.data.shared_watchlist_cache import get_shared_watchlist_cache
            cache_service = get_shared_watchlist_cache()

            watchlist_data = await cache_service.get_watchlist_data()

            if not watchlist_data['success']:
                await interaction.followup.send(watchlist_data['error'], ephemeral=True)
                return

            current_prices = watchlist_data['prices']
            price_changes = {}

            for symbol, price in current_prices.items():
                if symbol in self.watchlist_cog.previous_prices:
                    old_price = self.watchlist_cog.previous_prices[symbol]

                    # Use unified percentage calculation service
                    from services.market.percentage_calculation_service import get_percentage_service
                    percentage_service = get_percentage_service()

                    result = percentage_service.calculate_percentage_change(price, old_price)
                    change_pct = result.value if result.is_valid else 0.0
                    price_changes[symbol] = change_pct
                else:
                    price_changes[symbol] = 0.0

            self.watchlist_cog.previous_prices.update(current_prices)

            embed = await self.watchlist_cog.create_enhanced_watchlist_embed(watchlist_data, price_changes, self.change_type)

            await interaction.edit_original_response(embed=embed, view=self)

            logger.info(f"Watchlist manually refreshed by {interaction.user}")

        except Exception as e:
            logger.error(f"Error refreshing watchlist: {e}")
            await interaction.followup.send("❌ Error refreshing watchlist data.", ephemeral=True)

    @discord.ui.button(label="🛑 Stop Auto-Refresh", style=discord.ButtonStyle.secondary)
    async def stop_refresh_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        try:
            if interaction.channel.id in self.watchlist_cog.bot.active_watchlists:
                del self.watchlist_cog.bot.active_watchlists[interaction.channel.id]

            for item in self.children:
                item.disabled = True

            await interaction.response.edit_message(view=self)
            await interaction.followup.send("🛑 Auto-refresh stopped for this watchlist.", ephemeral=True)

            logger.info(f"Auto-refresh stopped by {interaction.user} for channel {interaction.channel.id}")

        except Exception as e:
            logger.error(f"Error stopping auto-refresh: {e}")
            await interaction.response.send_message("❌ Error stopping auto-refresh.", ephemeral=True)

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True

async def setup(bot):
    await bot.add_cog(WatchlistCommands(bot))
